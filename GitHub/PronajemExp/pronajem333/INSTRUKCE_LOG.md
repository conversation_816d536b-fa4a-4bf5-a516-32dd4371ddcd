# Log instrukcí a změn - PronajemExp

## Datum: 2024-12-19

### Požadavek uživatele:
Uživatel požádal o vytvoření dokumentace architektury aplikace a souboru pro sledování instrukcí.

### Provedené akce:

#### 1. Analýza aplikace
- Prozkoumána struktura aplikace pomocí codebase-retrieval
- Identifikovány hlavní komponenty a soubory
- Analyzovány dependencies v requirements.txt
- Prozkoumána struktura adresářů

#### 2. Vytvoření dokumentace architektury (ARCHITEKTURA.md)
**Obsah dokumentace:**
- P<PERSON>ehled aplikace - Discord bot pro monitoring pronájmů
- Technologie - Python, Discord.py, web scraping knihovny
- Struktura projektu - hlavní soubory a adresáře
- Architektura systému:
  - Discord Bot logika (main.py)
  - Scraper systém s abstraktní třídou ScraperBase
  - Konfigurace přes environment proměnné
  - Ukládání dat pomocí OffersStorage
  - Logování včetně Discord loggeru
- Workflow aplikace - od inicializace po odesílání nabídek
- Podporované portály (9 realitních portálů)
- Dispozice bytů (1+kk až 5++)
- Bezpečnost a limity
- Deployment informace

#### 3. Vytvoření logu instrukcí (INSTRUKCE_LOG.md)
- Soubor pro sledování všech požadavků a změn
- Strukturovaný formát s datem, požadavkem a provedenými akcemi
- Připraven pro budoucí rozšiřování

### Zjištěné informace o aplikaci:

**Účel:** Discord bot automaticky monitoruje 9 realitních portálů, vyhledává nové nabídky pronájmu bytů podle nakonfigurovaných kritérií a odesílá je do Discord kanálu.

**Klíčové funkce:**
- Periodické scrapování realitních portálů
- Filtrování podle ceny (max 14000 Kč)
- Detekce duplikátů pomocí hashování
- Odesílání jako Discord embedy
- Logování chyb do Discord kanálu

**Technická architektura:**
- Modulární design s abstraktní třídou pro scrapery
- Konfigurace přes environment proměnné
- Perzistentní ukládání hashů nabídek
- Asynchronní Discord bot s periodickými úlohami

**Stav aplikace:**
- Funkční aplikace s 9 implementovanými scrapery
- Obsahuje log soubory s historií běhu
- Má nakonfigurované environment proměnné
- Používá moderní Discord.py s app_commands

### Status: ✅ DOKONČENO
Vytvořena kompletní dokumentace architektury a inicializován log instrukcí.

---

## Datum: 2025-06-05

### Požadavek uživatele:
Uživatel hlásil, že scraper pro Bravis nefunguje správně a požádal o diagnostiku a opravu problému.

### Provedené akce:

#### 1. Diagnostika problému
- Vytvořen testovací skript `test_bravis_scraper.py` pro detailní analýzu
- Identifikován problém: HTTP požadavky fungují (status 200), ale CSS selektory nenacházejí elementy
- Uloženo HTML pro ruční analýzu (`bravis_debug.html`)
- Zjištěno: Webová stránka Bravis změnila HTML strukturu

#### 2. Analýza změn HTML struktury
**Původní (nefunkční) selektory:**
```python
for item in soup.select("#search > .in > .itemslist > li"):
    link = item.select_one("a.main").get("href")
    title = "Pronájem " + params[1].find("strong").get_text().strip() + ", " + params[2].find("strong").get_text().strip()
    location = item.select_one(".location").get_text().strip()
    price = item.select_one(".price")
    image_url = item.select_one(".img > img").get("src")
```

**Nová struktura HTML:**
- Kontejner nabídek: `.itemslist > .initemslist > .item` (místo `#search > .in > .itemslist > li`)
- Hlavní odkaz: `a` (první odkaz v `.item`, místo `a.main`)
- Název: `h1` uvnitř `.desc` (místo složité konstrukce z parametrů)
- Lokace: `.desc .location` (místo `.location`)
- Cena: `.desc .price` (místo `.price`)
- Obrázek: `.image img` (místo `.img > img`)

#### 3. Oprava scraperu
**Soubor:** `scrapers/scraper_bravis.py`
**Metoda:** `get_latest_offers()`

**Klíčové změny:**
- Aktualizovány CSS selektory podle nové HTML struktury
- Přidáno lepší error handling s try-catch bloky
- Přidáno logování varování při chybách parsování jednotlivých nabídek
- Zjednodušeno získávání názvu nabídky (přímo z `h1` místo konstrukce z parametrů)

#### 4. Testování opravy
**Výsledky testů:**
- ✅ **2+kk, 2+1**: 21 nabídek
- ✅ **3+kk, 3+1**: 14 nabídek
- ✅ **1+kk, 1+1**: 21 nabídek
- ✅ **Všechny dispozice**: 21 nabídek

**Příklady získaných nabídek:**
- "Pronájem bytu 2+1, Brno - Královo Pole, Božetěchova ulice, balkón, sklep, vhodné pro pár" - 18 300 Kč
- "Pronájem bytu 2+KK, 64 m², ul. Mlýnská, zařízený, balkon" - 20 000 Kč
- "Pronájem bytu 3+1, Brno - Nový Lískovec, Oblá ulice, balkón, komora a sklep" - 24 900 Kč

#### 5. Úklid
- Odstraněny testovací soubory (`test_bravis_scraper.py`, `test_bravis_final.py`, `bravis_debug.html`)

### Technické detaily opravy:

**Před opravou:** Scraper vracel 0 nabídek kvůli neplatným CSS selektorům
**Po opravě:** Scraper správně získává 14-21 nabídek podle dispozice

**Hlavní změny v kódu:**
```python
# Nové selektory
for item in soup.select(".itemslist .initemslist .item"):
    try:
        main_link = item.select_one("a")
        title = item.select_one(".desc h1").get_text().strip()
        location = item.select_one(".desc .location").get_text().strip()
        price_element = item.select_one(".desc .price")
        price_text = [text for text in price_element.stripped_strings][0]
        price = int(re.sub(r"[^\d]", "", price_text))
        image_url = urljoin(self.base_url, item.select_one(".image img").get("src"))

        # Vytvoření RentalOffer objektu...
    except Exception as e:
        logging.warning(f"Chyba při parsování nabídky z Bravis: {e}")
        continue
```

### Status: ✅ DOKONČENO
Bravis scraper byl úspěšně opraven a nyní funguje správně. Problém byl způsoben změnou HTML struktury na webu Bravis, která vyžadovala aktualizaci CSS selektorů.

---

## Šablona pro budoucí záznamy:

### Datum: YYYY-MM-DD
### Požadavek uživatele:
[Popis požadavku]

### Provedené akce:
[Seznam akcí]

### Status: [DOKONČENO/PROBÍHÁ/ČEKÁ]
[Poznámky]

---
