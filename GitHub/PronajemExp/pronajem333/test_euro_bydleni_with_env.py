#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.scraper_euro_bydleni import ScraperEuroBydleni
from disposition import Disposition
import logging
from dotenv import load_dotenv

# Načtení .env souboru
load_dotenv(".env")

# Nastavení logování
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_dispositions_from_env():
    """Parsuje dispozice z .env souboru stejně jako config.py"""
    dispositions_str = os.getenv("DISPOSITIONS", "")
    print(f"Raw DISPOSITIONS z .env: '{dispositions_str}'")

    _str_to_disposition_map = {
        "1+kk": Disposition.FLAT_1KK,
        "1+1": Disposition.FLAT_1,
        "2+kk": Disposition.FLAT_2KK,
        "2+1": Disposition.FLAT_2,
        "3+kk": Disposition.FLAT_3KK,
        "3+1": Disposition.FLAT_3,
        "4+kk": Disposition.FLAT_4KK,
        "4+1": Disposition.FLAT_4,
        "5++": Disposition.FLAT_5_UP,
        "others": Disposition.FLAT_OTHERS
    }

    # Kombinace dispozic pomocí bitového OR (stejně jako v config.py)
    result = Disposition.NONE
    for disp_str in dispositions_str.split(","):
        disp_str = disp_str.strip()
        if disp_str in _str_to_disposition_map:
            result |= _str_to_disposition_map[disp_str]

    return result

def test_euro_bydleni_with_env_config():
    print("=== Test Euro Bydlení Scraper s konfiguracemi z .env ===")

    # Parsování dispozic z .env
    dispositions = parse_dispositions_from_env()
    print(f"Načtené dispozice z .env: {dispositions}")

    # Zobrazit, které konkrétní dispozice jsou aktivní
    active_dispositions = []
    if dispositions & Disposition.FLAT_1KK:
        active_dispositions.append("1+kk")
    if dispositions & Disposition.FLAT_1:
        active_dispositions.append("1+1")
    if dispositions & Disposition.FLAT_2KK:
        active_dispositions.append("2+kk")
    if dispositions & Disposition.FLAT_2:
        active_dispositions.append("2+1")
    if dispositions & Disposition.FLAT_3KK:
        active_dispositions.append("3+kk")
    if dispositions & Disposition.FLAT_3:
        active_dispositions.append("3+1")
    if dispositions & Disposition.FLAT_4KK:
        active_dispositions.append("4+kk")
    if dispositions & Disposition.FLAT_4:
        active_dispositions.append("4+1")
    if dispositions & Disposition.FLAT_5_UP:
        active_dispositions.append("5++")
    if dispositions & Disposition.FLAT_OTHERS:
        active_dispositions.append("others")

    print(f"Aktivní dispozice: {', '.join(active_dispositions)}")

    # Vytvoření scraperu s přesnými nastaveními z .env
    scraper = ScraperEuroBydleni(dispositions)

    print(f"\n--- Test s dispozicemi z .env: {', '.join(active_dispositions)} ---")

    try:
        # Test HTTP požadavku
        response = scraper.build_response()
        print(f"HTTP Status: {response.status_code}")
        print(f"Response length: {len(response.text)} characters")

        if response.status_code != 200:
            print(f"❌ HTTP chyba: {response.status_code}")
            return

        # Uložení HTML pro debug
        with open("euro_bydleni_debug_env_config.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print(f"HTML uloženo pro debug")

        # Test parsování nabídek
        offers = scraper.get_latest_offers()
        print(f"Počet nabídek: {len(offers)}")

        if offers:
            print("✅ Scraper funguje s konfiguracemi z .env!")
            print("\nPříklady nabídek:")
            for i, offer in enumerate(offers[:5]):  # Zobraz prvních 5
                print(f"  {i+1}. {offer.title} - {offer.price} Kč")
                print(f"     Lokace: {offer.location}")
                print(f"     Link: {offer.link}")
                print(f"     Obrázek: {offer.image_url}")
                print()

            # Kontrola, že se získávají správné dispozice
            print("Kontrola dispozic v nabídkách:")
            found_dispositions = set()
            for offer in offers:
                # Extrakce dispozice z titulku
                import re
                disposition_match = re.search(r'(\d+\+\w+)', offer.title)
                if disposition_match:
                    found_dispositions.add(disposition_match.group(1))

            print(f"Nalezené dispozice v nabídkách: {', '.join(sorted(found_dispositions))}")

            # Ověření, že nalezené dispozice odpovídají konfiguraci
            expected_dispositions = set(active_dispositions)
            if "2+kk" in expected_dispositions or "2+1" in expected_dispositions:
                matching_found = found_dispositions.intersection({"2+kk", "2+1"})
                if matching_found:
                    print(f"✅ Nalezeny očekávané dispozice: {', '.join(matching_found)}")
                else:
                    print(f"⚠️  Nebyly nalezeny očekávané dispozice 2+kk/2+1")
        else:
            print("❌ Žádné nabídky nenalezeny!")

    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_euro_bydleni_with_env_config()
